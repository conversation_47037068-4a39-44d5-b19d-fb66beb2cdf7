import pool from '../config/db.js';
import { v4 as uuidv4 } from 'uuid';

class Attendance {
  constructor(attendance) {
    this.id = attendance.id || uuidv4();
    this.workspaceId = attendance.workspaceId;
    this.userId = attendance.userId;
    this.date = attendance.date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    this.checkInTime = attendance.checkInTime || null;
    this.checkInMessage = attendance.checkInMessage || null;
    this.checkOutTime = attendance.checkOutTime || null;
    this.checkOutMessage = attendance.checkOutMessage || null;
    this.status = attendance.status || 'absent';
    this.hoursWorked = attendance.hoursWorked || null;
    this.createdAt = attendance.createdAt || new Date();
    this.updatedAt = attendance.updatedAt || new Date();
  }

  // Create a new attendance record
  async create() {
    const query = `
      INSERT INTO attendance (
        id, workspaceId, userId, date, checkInTime,
        checkInMessage, checkOutTime, checkOutMessage,
        status, hoursWorked, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      this.id, this.workspaceId, this.userId, this.date, this.checkInTime,
      this.checkInMessage, this.checkOutTime, this.checkOutMessage,
      this.status, this.hoursWorked, this.createdAt, this.updatedAt
    ];

    try {
      const [result] = await pool.query(query, values);
      return { id: this.id, ...this };
    } catch (error) {
      throw error;
    }
  }

  // Find attendance record by ID
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM attendance WHERE id = ?', [id]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Find attendance record for a user on a specific date
  static async findByUserAndDate(userId, date) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM attendance WHERE userId = ? AND date = ?',
        [userId, date]
      );
      return rows.length ? rows[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Get all attendance records
  static async findAll(filters = {}) {
    try {
      let query = 'SELECT * FROM attendance';
      const values = [];

      // Add filters if provided
      if (Object.keys(filters).length > 0) {
        const conditions = [];

        if (filters.workspaceId) {
          conditions.push('workspaceId = ?');
          values.push(filters.workspaceId);
        }

        if (filters.userId) {
          conditions.push('userId = ?');
          values.push(filters.userId);
        }

        if (filters.date) {
          conditions.push('date = ?');
          values.push(filters.date);
        }

        if (filters.status) {
          conditions.push('status = ?');
          values.push(filters.status);
        }

        if (filters.startDate && filters.endDate) {
          conditions.push('date BETWEEN ? AND ?');
          values.push(filters.startDate, filters.endDate);
        }

        if (conditions.length > 0) {
          query += ' WHERE ' + conditions.join(' AND ');
        }
      }

      // Add order by
      query += ' ORDER BY date DESC, checkInTime DESC';

      const [rows] = await pool.query(query, values);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Update attendance record
  static async update(id, attendanceData) {
    try {
      // Don't allow updating the ID
      delete attendanceData.id;

      // Add updatedAt timestamp
      attendanceData.updatedAt = new Date();

      // Build the query dynamically based on the fields to update
      const fields = Object.keys(attendanceData)
        .map(key => `${key} = ?`)
        .join(', ');

      const values = [...Object.values(attendanceData), id];

      const query = `UPDATE attendance SET ${fields} WHERE id = ?`;

      const [result] = await pool.query(query, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Record check-in
  static async checkIn(workspaceId, userId, message = null) {
    try {
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      const now = new Date();

      // Check if there's already an attendance record for today
      let attendance = await this.findByUserAndDate(userId, today);

      if (attendance) {
        // Update existing record
        if (!attendance.checkInTime) {
          const hoursWorked = attendance.checkOutTime
            ? (new Date(attendance.checkOutTime) - now) / (1000 * 60 * 60)
            : null;

          await this.update(attendance.id, {
            checkInTime: now,
            checkInMessage: message,
            status: 'present',
            hoursWorked: hoursWorked
          });
        }

        return await this.findById(attendance.id);
      } else {
        // Create new record
        const newAttendance = new Attendance({
          workspaceId,
          userId,
          date: today,
          checkInTime: now,
          checkInMessage: message,
          status: 'present'
        });

        return await newAttendance.create();
      }
    } catch (error) {
      throw error;
    }
  }

  // Record check-out
  static async checkOut(workspaceId, userId, message = null) {
    try {
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      const now = new Date();

      // Check if there's already an attendance record for today
      let attendance = await this.findByUserAndDate(userId, today);

      if (attendance) {
        // Calculate hours worked if check-in time exists
        let hoursWorked = null;
        if (attendance.checkInTime) {
          const checkInTime = new Date(attendance.checkInTime);
          hoursWorked = (now - checkInTime) / (1000 * 60 * 60);
        }

        // Update existing record
        await this.update(attendance.id, {
          checkOutTime: now,
          checkOutMessage: message,
          hoursWorked: hoursWorked
        });

        return await this.findById(attendance.id);
      } else {
        // Create new record (unusual case - checking out without checking in)
        const newAttendance = new Attendance({
          workspaceId,
          userId,
          date: today,
          checkOutTime: now,
          checkOutMessage: message,
          status: 'present'
        });

        return await newAttendance.create();
      }
    } catch (error) {
      throw error;
    }
  }

  // Get daily overview for a workspace
  static async getDailyOverview(workspaceId, date = null) {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD

      // Get all attendance records for the workspace on the specified date
      const [records] = await pool.query(
        `SELECT a.*, u.name, u.avatar, u.position
         FROM attendance a
         JOIN users u ON a.userId = u.id
         WHERE a.workspaceId = ? AND a.date = ?
         ORDER BY a.checkInTime ASC`,
        [workspaceId, targetDate]
      );

      // Get summary statistics
      const [summary] = await pool.query(
        `SELECT
           COUNT(DISTINCT u.id) as totalUsers,
           COUNT(DISTINCT CASE WHEN a.status = 'present' THEN a.userId END) as presentCount,
           COUNT(DISTINCT CASE WHEN a.status = 'absent' THEN a.userId END) as absentCount,
           COUNT(DISTINCT CASE WHEN a.status = 'late' THEN a.userId END) as lateCount,
           COUNT(DISTINCT CASE WHEN a.status = 'on leave' THEN a.userId END) as onLeaveCount
         FROM users u
         LEFT JOIN attendance a ON u.id = a.userId AND a.date = ?
         WHERE u.workspaceId = ?`,
        [targetDate, workspaceId]
      );

      return {
        date: targetDate,
        summary: summary[0],
        records
      };
    } catch (error) {
      throw error;
    }
  }

  // Record project hours
  static async recordProjectHours(attendanceId, projectId, hours) {
    try {
      const id = uuidv4();

      const query = `
        INSERT INTO project_hours (id, attendanceId, projectId, hours)
        VALUES (?, ?, ?, ?)
      `;

      const [result] = await pool.query(query, [id, attendanceId, projectId, hours]);

      if (result.affectedRows > 0) {
        return {
          id,
          attendanceId,
          projectId,
          hours
        };
      }

      return null;
    } catch (error) {
      throw error;
    }
  }

  // Get project hours for an attendance record
  static async getProjectHours(attendanceId) {
    try {
      const query = `
        SELECT ph.*, p.title as projectTitle
        FROM project_hours ph
        JOIN projects p ON ph.projectId = p.id
        WHERE ph.attendanceId = ?
      `;

      const [rows] = await pool.query(query, [attendanceId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Get hour map (hours worked per project) for a user in a date range
  static async getHourMap(userId, startDate, endDate) {
    try {
      const query = `
        SELECT
          p.id as projectId,
          p.title as projectTitle,
          SUM(ph.hours) as totalHours
        FROM project_hours ph
        JOIN attendance a ON ph.attendanceId = a.id
        JOIN projects p ON ph.projectId = p.id
        WHERE a.userId = ? AND a.date BETWEEN ? AND ?
        GROUP BY p.id, p.title
      `;

      const [rows] = await pool.query(query, [userId, startDate, endDate]);
      return rows;
    } catch (error) {
      throw error;
    }
  }
}

export default Attendance;
