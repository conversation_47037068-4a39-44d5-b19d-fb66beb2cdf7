import Analytics from "../models/analytics.model.js";
import { Op } from "sequelize";
import { sequelize } from "../config/database.js";

// Track page visit
const trackPageVisit = async (req, res) => {
  try {
    // Only track in production environment
    if (process.env.NODE_ENV !== 'production') {
      return res.status(200).json({
        success: false,
        message: 'Analytics tracking disabled in development'
      });
    }

    const { page, referrer } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get("User-Agent");

    const analyticsRecord = await Analytics.create({
      eventType: "page_visit",
      eventData: {
        page: page || "landing",
        referrer: referrer || null,
      },
      ipAddress,
      userAgent,
    });

    res.status(200).json({
      success: true,
      message: "Page visit tracked successfully",
      data: {
        id: analyticsRecord.id,
        timestamp: analyticsRecord.timestamp,
      },
    });
  } catch (error) {
    console.error("Track page visit error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to track page visit",
    });
  }
};

// Track login button click
const trackLoginClick = async (req, res) => {
  try {
    // Only track in production environment
    if (process.env.NODE_ENV !== 'production') {
      return res.status(200).json({
        success: false,
        message: 'Analytics tracking disabled in development'
      });
    }

    const { source } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get("User-Agent");

    const analyticsRecord = await Analytics.create({
      eventType: "login_click",
      eventData: {
        source: source || "header",
      },
      ipAddress,
      userAgent,
    });

    res.status(200).json({
      success: true,
      message: "Login click tracked successfully",
      data: {
        id: analyticsRecord.id,
        timestamp: analyticsRecord.timestamp,
      },
    });
  } catch (error) {
    console.error("Track login click error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to track login click",
    });
  }
};

// Track pricing button click
const trackPricingClick = async (req, res) => {
  try {
    // Only track in production environment
    if (process.env.NODE_ENV !== 'production') {
      return res.status(200).json({
        success: false,
        message: 'Analytics tracking disabled in development'
      });
    }

    const { planName, planPrice, action } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get("User-Agent");

    const analyticsRecord = await Analytics.create({
      eventType: "pricing_click",
      eventData: {
        planName: planName || "unknown",
        planPrice: planPrice || null,
        action: action || "buy_now",
      },
      ipAddress,
      userAgent,
    });

    res.status(200).json({
      success: true,
      message: "Pricing click tracked successfully",
      data: {
        id: analyticsRecord.id,
        timestamp: analyticsRecord.timestamp,
      },
    });
  } catch (error) {
    console.error("Track pricing click error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to track pricing click",
    });
  }
};

// Get analytics summary (for admin dashboard)
const getAnalyticsSummary = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    let whereClause = {};
    if (startDate && endDate) {
      whereClause.timestamp = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    const summary = await Analytics.findAll({
      where: whereClause,
      attributes: [
        "eventType",
        [sequelize.fn("COUNT", sequelize.col("id")), "count"],
        [sequelize.fn("DATE", sequelize.col("timestamp")), "date"],
      ],
      group: ["eventType", sequelize.fn("DATE", sequelize.col("timestamp"))],
      order: [[sequelize.fn("DATE", sequelize.col("timestamp")), "DESC"]],
    });

    res.status(200).json({
      success: true,
      data: summary,
    });
  } catch (error) {
    console.error("Get analytics summary error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get analytics summary",
    });
  }
};

export {
  trackPageVisit,
  trackLoginClick,
  trackPricingClick,
  getAnalyticsSummary,
};
