# TeamCheck - Team Management Application

## Overview

TeamCheck is a streamlined team management web application designed to help managers monitor their team's activities, projects, performance, and attendance. The platform provides a clean, intuitive dashboard that gives managers a 360-degree view of their team's status while offering a simple interface for employees to report their work and manage their tasks.

## Purpose

Small to medium-sized companies often lack simple, non-bloated tools to manage team activities like attendance, task progress, and performance without the complexity of full-scale project management platforms. TeamCheck addresses this gap by providing:

- A centralized view of team check-ins, leaves, tasks, and performance
- Simple tools for tracking attendance and project progress
- Performance evaluation capabilities
- Employee management features
- Multi-workspace support for organizations with different teams or departments

## Core Features

### Workspace Management

The workspace system allows organizations to:

- Create separate workspaces for different teams, departments, or projects
- Customize workspace settings including check-in hours and message formats
- Manage users within each workspace independently
- Keep data segregated between workspaces for better organization

### Dashboard

The main dashboard provides an at-a-glance overview of:

- Team statistics (members, present today, on leave, tasks due)
- Today's check-ins with employee messages
- Active projects with progress indicators
- Performance evaluations (upcoming and recent)
- Leave management (upcoming and requested)
- Workspace selection for users with access to multiple workspaces

### Projects Management

The projects section allows managers to:

- Create and manage projects with detailed information
- View projects in a Kanban board or list view
- Create and assign tasks to team members
- Track task status and progress
- Set milestones for projects

Key components:

- **Kanban Board**: Drag-and-drop interface for visualizing task status (Not Started, In Progress, Completed)
- **Task Management**: Create, edit, and delete tasks with descriptions, assignees, due dates, and checklists
- **Project Details**: View comprehensive project information including team members and progress

### Performance Evaluations

The performance evaluation system enables:

- Creating periodic evaluations for employees
- Project-specific reviews to evaluate performance on particular tasks
- Rating employees and providing feedback
- Tracking performance history and improvement

### Activities Tracking

The activities section provides:

- Attendance tracking with check-ins/check-outs
- Daily overview of team attendance
- Calendar view showing user check-ins
- Detailed check-in/check-out logs
- HourMap feature showing hours worked per employee with project breakdown

### Employee Management

Managers can:

- View employee details and status
- Track employee attendance and performance
- Manage employee roles and permissions

### Leave Management

The leave management system allows:

- Viewing upcoming and requested leaves
- Approving or denying leave requests
- Setting leave policies

## Technical Implementation

### Frontend Architecture

The application is built with:

- **React**: For building the user interface
- **React Router**: For navigation and routing
- **Tailwind CSS**: For styling and responsive design
- **Lucide React**: For icons
- **React Beautiful DND**: For drag-and-drop functionality in the Kanban board
- **Context API**: For managing workspace context and user permissions

### Backend Architecture

The application backend is built with:

- **Node.js**: Runtime environment
- **Express.js**: Web framework
- **MySQL**: Relational database for data storage
- **JWT**: For authentication and authorization
- **RESTful API**: For communication between frontend and backend

### Key Components

1. **Dashboard Layout**:

   - Sidebar navigation
   - Top navigation bar with workspace selector
   - Main content area

2. **Workspace Components**:

   - WorkspaceSelector: Dropdown for switching between workspaces
   - CreateWorkspaceModal: For creating new workspaces
   - WorkspaceSettingsPage: For configuring workspace settings
   - WorkspaceUsersPage: For managing users within a workspace

3. **Modal System**:

   - SlideInModal: Modals that slide in from the right side of the screen
   - Standard Modal: Center-screen modals for various purposes

4. **Project Components**:

   - CreateProjectModal: For creating new projects
   - KanbanBoard: For visualizing and managing tasks
   - TaskCard: For displaying task information
   - CreateTaskModal: For creating new tasks
   - TaskDetailsModal: For viewing and editing task details

5. **Performance Evaluation Components**:

   - CreateEvaluationModal: For creating new evaluations
   - EvaluationDetailsPage: For viewing and editing evaluation details

6. **Activities Components**:
   - AttendanceCalendar: Calendar view showing user check-ins
   - CheckInLogsPage: Detailed logs of check-ins and check-outs
   - HourMapPage: Breakdown of hours worked per employee and project

### Styling

The application uses a dark theme with:

- Glass-card design for main components
- Consistent color scheme with accent colors
- Responsive design for various screen sizes

Color palette:

- Background: #0a0a0a
- Surface: #141414
- Surface-2: #1a1a1a
- Surface-3: #222222
- Accent colors for status indicators and highlights

## Recent Implementations

Recent work on the application has focused on:

1. **Modal Improvements**:

   - Converting standard modals to slide-in modals for a more modern UX
   - Ensuring consistent styling across all modals
   - Improving the CreateProjectModal, CreateTaskModal, and CreateEvaluationModal components

2. **Kanban Board Enhancements**:

   - Increasing the height of the Kanban board to reduce scrolling
   - Improving the drag-and-drop experience
   - Enhancing the visual design of task cards

3. **UI/UX Refinements**:
   - Ensuring consistent styling across the application
   - Improving color contrast for better readability
   - Matching active state styling with the sidebar navigation

## Future Development

Planned future enhancements include:

1. **Backend Integration**:

   - Connecting the frontend to a robust backend API
   - Implementing authentication and authorization with workspace-level permissions
   - Setting up real-time data synchronization
   - Cross-workspace analytics for organization-wide insights

2. **Advanced Analytics**:

   - Team performance metrics and trends
   - Attendance patterns and insights
   - Project completion rates and bottlenecks
   - Comparative analytics between workspaces

3. **Mobile Responsiveness**:

   - Enhancing the mobile experience for on-the-go access
   - Potentially developing a dedicated mobile app
   - Workspace switching on mobile devices

4. **Integration Capabilities**:
   - API for third-party integrations
   - Calendar integration (Google Calendar, Outlook)
   - Email notifications
   - Potential future Slack integration

## Conclusion

TeamCheck provides a streamlined solution for team management that balances functionality with simplicity. By focusing on the core needs of managers and team members across different workspaces, the application offers a clean, intuitive interface for monitoring team activities, projects, and performance without the complexity of enterprise-level project management tools.
