import express from 'express';
import * as companyController from '../controllers/company.controller.js';
import { authenticate, authorize, companyAccess } from '../middleware/auth.middleware.js';

const router = express.Router();

// Get all companies for current user
router.get('/', authenticate, companyController.getCompanies);

// Create new company
router.post('/', authenticate, companyController.createCompany);

// Get company by ID
router.get('/:id', authenticate, companyAccess, companyController.getCompanyById);

// Update company
router.put('/:id', authenticate, companyAccess, companyController.updateCompany);

// Delete company
router.delete('/:id', authenticate, companyAccess, companyController.deleteCompany);

// Get all users in company
router.get('/:id/users', authenticate, companyAccess, companyController.getCompanyUsers);

// Invite user to company
router.post('/:id/invite', authenticate, companyAccess, authorize(['admin', 'manager']), companyController.inviteUserToCompany);

export default router;
