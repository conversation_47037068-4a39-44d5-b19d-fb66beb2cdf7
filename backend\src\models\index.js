import { sequelize } from "../config/database.js";
import User from "./user.model.js";
import Company from "./company.model.js";
import {
  Project,
  ProjectTeamMember,
  ProjectColumn,
  ProjectMilestone,
} from "./project.model.js";
import Analytics from "./analytics.model.js";
import Waitlist from "./waitlist.model.js";

// Define associations
// User <-> Company
User.belongsTo(Company, { foreignKey: "companyId" });
Company.hasMany(User, { foreignKey: "companyId" });

// User <-> Project (creator)
Project.belongsTo(User, { foreignKey: "createdBy", as: "Creator" });
User.hasMany(Project, { foreignKey: "createdBy", as: "CreatedProjects" });

// Project <-> Company
Project.belongsTo(Company, { foreignKey: "companyId" });
Company.hasMany(Project, { foreignKey: "companyId" });

// Project <-> ProjectTeamMember
Project.hasMany(ProjectTeamMember, { foreignKey: "projectId" });
ProjectTeamMember.belongsTo(Project, { foreignKey: "projectId" });

// User <-> ProjectTeamMember
User.hasMany(ProjectTeamMember, { foreignKey: "userId" });
ProjectTeamMember.belongsTo(User, { foreignKey: "userId" });

// Project <-> ProjectColumn
Project.hasMany(ProjectColumn, { foreignKey: "projectId" });
ProjectColumn.belongsTo(Project, { foreignKey: "projectId" });

// Project <-> ProjectMilestone
Project.hasMany(ProjectMilestone, { foreignKey: "projectId" });
ProjectMilestone.belongsTo(Project, { foreignKey: "projectId" });

// Export models
export default {
  sequelize,
  User,
  Company,
  Project,
  ProjectTeamMember,
  ProjectColumn,
  ProjectMilestone,
  Analytics,
  Waitlist,
};
