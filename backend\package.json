{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "start": "node src/server.js", "dev": "nodemon src/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "sequelize": "^6.37.7", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}