import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

// Import models
import models from '../models/index.js';

async function createDatabaseIfNotExists() {
  try {
    // Create a connection without specifying a database
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD
    });

    // Create database if it doesn't exist
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME}`);
    console.log(`Database ${process.env.DB_NAME} created or already exists`);

    // Close the connection
    await connection.end();
  } catch (error) {
    console.error('Error creating database:', error);
    throw error;
  }
}

async function initializeDatabase(options = {}) {
  try {
    console.log('Starting database initialization...');

    // First create the database if it doesn't exist
    await createDatabaseIfNotExists();

    // Sync options
    const syncOptions = {
      // By default, only create tables if they don't exist
      force: options.force || false,  // If true, drops tables before recreating them
      alter: options.alter || false   // If true, alters tables to match models
    };

    // Sync all models with the database
    console.log(`Syncing models with database... (force: ${syncOptions.force}, alter: ${syncOptions.alter})`);
    await models.sequelize.sync(syncOptions);

    console.log('Database initialization completed successfully!');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
}

// Run the initialization if this script is executed directly
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);

if (process.argv[1] === __filename) {
  initializeDatabase()
    .then(() => process.exit(0))
    .catch(err => {
      console.error('Failed to initialize database:', err);
      process.exit(1);
    });
}

export { initializeDatabase };
