import pool from '../config/db.js';
import { v4 as uuidv4 } from 'uuid';

class Task {
  constructor(task) {
    this.id = task.id || uuidv4();
    this.workspaceId = task.workspaceId;
    this.projectId = task.projectId;
    this.title = task.title;
    this.description = task.description || null;
    this.status = task.status || 'not started';
    this.columnId = task.columnId || null;
    this.dueDate = task.dueDate || null;
    this.createdBy = task.createdBy;
    this.createdAt = task.createdAt || new Date();
    this.updatedAt = task.updatedAt || new Date();
  }

  // Create a new task
  async create() {
    const query = `
      INSERT INTO tasks (
        id, workspaceId, projectId, title, description,
        status, columnId, dueDate, createdBy, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      this.id, this.workspaceId, this.projectId, this.title, this.description,
      this.status, this.columnId, this.dueDate, this.createdBy, this.createdAt, this.updatedAt
    ];

    try {
      const [result] = await pool.query(query, values);
      return { id: this.id, ...this };
    } catch (error) {
      throw error;
    }
  }

  // Find task by ID
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM tasks WHERE id = ?', [id]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Get all tasks
  static async findAll(filters = {}) {
    try {
      let query = 'SELECT * FROM tasks';
      const values = [];

      // Add filters if provided
      if (Object.keys(filters).length > 0) {
        const conditions = [];

        if (filters.workspaceId) {
          conditions.push('workspaceId = ?');
          values.push(filters.workspaceId);
        }

        if (filters.projectId) {
          conditions.push('projectId = ?');
          values.push(filters.projectId);
        }

        if (filters.status) {
          conditions.push('status = ?');
          values.push(filters.status);
        }

        if (filters.columnId) {
          conditions.push('columnId = ?');
          values.push(filters.columnId);
        }

        if (filters.createdBy) {
          conditions.push('createdBy = ?');
          values.push(filters.createdBy);
        }

        if (conditions.length > 0) {
          query += ' WHERE ' + conditions.join(' AND ');
        }
      }

      // Add order by
      query += ' ORDER BY createdAt DESC';

      const [rows] = await pool.query(query, values);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Get tasks assigned to a user
  static async findAssignedToUser(userId) {
    try {
      const query = `
        SELECT t.*
        FROM tasks t
        JOIN task_assignees ta ON t.id = ta.taskId
        WHERE ta.userId = ?
        ORDER BY t.createdAt DESC
      `;

      const [rows] = await pool.query(query, [userId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Update task
  static async update(id, taskData) {
    try {
      // Don't allow updating the ID
      delete taskData.id;

      // Add updatedAt timestamp
      taskData.updatedAt = new Date();

      // Build the query dynamically based on the fields to update
      const fields = Object.keys(taskData)
        .map(key => `${key} = ?`)
        .join(', ');

      const values = [...Object.values(taskData), id];

      const query = `UPDATE tasks SET ${fields} WHERE id = ?`;

      const [result] = await pool.query(query, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete task
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM tasks WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get task assignees
  static async getAssignees(taskId) {
    try {
      const query = `
        SELECT u.id, u.name, u.email, u.position, u.avatar
        FROM task_assignees ta
        JOIN users u ON ta.userId = u.id
        WHERE ta.taskId = ?
      `;

      const [rows] = await pool.query(query, [taskId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Add assignee to task
  static async addAssignee(taskId, userId) {
    try {
      const assigneeId = uuidv4();
      const query = `
        INSERT INTO task_assignees (id, taskId, userId)
        VALUES (?, ?, ?)
      `;

      const [result] = await pool.query(query, [assigneeId, taskId, userId]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Remove assignee from task
  static async removeAssignee(taskId, userId) {
    try {
      const query = `
        DELETE FROM task_assignees
        WHERE taskId = ? AND userId = ?
      `;

      const [result] = await pool.query(query, [taskId, userId]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get task comments
  static async getComments(taskId) {
    try {
      const query = `
        SELECT tc.*, u.name, u.avatar
        FROM task_comments tc
        JOIN users u ON tc.userId = u.id
        WHERE tc.taskId = ?
        ORDER BY tc.createdAt ASC
      `;

      const [rows] = await pool.query(query, [taskId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Add comment to task
  static async addComment(taskId, userId, text) {
    try {
      const commentId = uuidv4();
      const createdAt = new Date();

      const query = `
        INSERT INTO task_comments (id, taskId, userId, text, createdAt)
        VALUES (?, ?, ?, ?, ?)
      `;

      const [result] = await pool.query(query, [commentId, taskId, userId, text, createdAt]);

      if (result.affectedRows > 0) {
        // Get the user info for the response
        const [userRows] = await pool.query(
          'SELECT name, avatar FROM users WHERE id = ?',
          [userId]
        );

        return {
          id: commentId,
          taskId,
          userId,
          text,
          createdAt,
          name: userRows[0]?.name,
          avatar: userRows[0]?.avatar
        };
      }

      return null;
    } catch (error) {
      throw error;
    }
  }

  // Get task checklist items
  static async getChecklistItems(taskId) {
    try {
      const query = `
        SELECT *
        FROM task_checklist_items
        WHERE taskId = ?
      `;

      const [rows] = await pool.query(query, [taskId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Add checklist item to task
  static async addChecklistItem(taskId, text) {
    try {
      const itemId = uuidv4();

      const query = `
        INSERT INTO task_checklist_items (id, taskId, text, completed)
        VALUES (?, ?, ?, false)
      `;

      const [result] = await pool.query(query, [itemId, taskId, text]);

      if (result.affectedRows > 0) {
        return {
          id: itemId,
          taskId,
          text,
          completed: false
        };
      }

      return null;
    } catch (error) {
      throw error;
    }
  }

  // Update checklist item
  static async updateChecklistItem(itemId, completed) {
    try {
      const query = `
        UPDATE task_checklist_items
        SET completed = ?
        WHERE id = ?
      `;

      const [result] = await pool.query(query, [completed, itemId]);

      if (result.affectedRows > 0) {
        const [rows] = await pool.query(
          'SELECT * FROM task_checklist_items WHERE id = ?',
          [itemId]
        );

        return rows[0];
      }

      return null;
    } catch (error) {
      throw error;
    }
  }

  // Get task with details (including assignees, comments, checklist items)
  static async getTaskWithDetails(taskId) {
    try {
      // Get task
      const task = await this.findById(taskId);
      if (!task) return null;

      // Get assignees
      const assignees = await this.getAssignees(taskId);

      // Get comments
      const comments = await this.getComments(taskId);

      // Get checklist items
      const checklistItems = await this.getChecklistItems(taskId);

      return {
        ...task,
        assignees,
        comments,
        checklistItems
      };
    } catch (error) {
      throw error;
    }
  }
}

export default Task;
