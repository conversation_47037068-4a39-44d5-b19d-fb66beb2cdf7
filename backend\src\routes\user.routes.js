import express from 'express';
import * as userController from '../controllers/user.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Get all users
router.get('/', authenticate, userController.getUsers);

// Invite new user
router.post('/invite', authenticate, authorize(['admin', 'manager']), userController.inviteUser);

// Get users by company
router.get('/company/:companyId', authenticate, userController.getUsersByCompany);

// Get user by ID
router.get('/:id', authenticate, userController.getUserById);

// Update user
router.put('/:id', authenticate, userController.updateUser);

// Delete user
router.delete('/:id', authenticate, authorize(['admin', 'manager']), userController.deleteUser);

export default router;
