import express from "express";
import * as waitlistController from "../controllers/waitlist.controller.js";
import { authenticate, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

// Public route for joining waitlist
router.post("/join", waitlistController.joinWaitlist);

// Public route for basic stats (for statistics page)
router.get("/stats", waitlistController.getWaitlistStats);

// Protected routes for viewing detailed waitlist data (admin only)
router.get(
  "/entries",
  authenticate,
  authorize(["admin"]),
  waitlistController.getAllWaitlistEntries
);

export default router;
