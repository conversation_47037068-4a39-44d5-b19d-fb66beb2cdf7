import express from 'express';
import * as projectController from '../controllers/project.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Get all projects
router.get('/', authenticate, projectController.getProjects);

// Create new project
router.post('/', authenticate, authorize(['admin', 'manager']), projectController.createProject);

// Get project by ID
router.get('/:id', authenticate, projectController.getProjectById);

// Update project
router.put('/:id', authenticate, projectController.updateProject);

// Delete project
router.delete('/:id', authenticate, projectController.deleteProject);

// Add member to project
router.post('/:id/members', authenticate, authorize(['admin', 'manager']), projectController.addProjectMember);

// Remove member from project
router.delete('/:id/members/:userId', authenticate, authorize(['admin', 'manager']), projectController.removeProjectMember);

export default router;
