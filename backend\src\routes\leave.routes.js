import express from 'express';
import * as leaveController from '../controllers/leave.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Get leave requests
router.get('/', authenticate, leaveController.getLeaves);

// Create leave request
router.post('/', authenticate, leaveController.createLeave);

// Get leave by ID
router.get('/:id', authenticate, leaveController.getLeaveById);

// Update leave request
router.put('/:id', authenticate, leaveController.updateLeave);

// Approve leave
router.put('/:id/approve', authenticate, authorize(['admin', 'manager']), leaveController.approveLeave);

// Deny leave
router.put('/:id/deny', authenticate, authorize(['admin', 'manager']), leaveController.denyLeave);

export default router;
