import express from "express";
import * as analyticsController from "../controllers/analytics.controller.js";

const router = express.Router();

// Public routes for tracking (no authentication required)
router.post("/track/page-visit", analyticsController.trackPageVisit);
router.post("/track/login-click", analyticsController.trackLoginClick);
router.post("/track/pricing-click", analyticsController.trackPricingClick);

// Public route for viewing analytics summary (for statistics page)
router.get("/summary", analyticsController.getAnalyticsSummary);

export default router;
