import express from 'express';
import * as taskController from '../controllers/task.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Get all tasks
router.get('/', authenticate, taskController.getTasks);

// Create new task
router.post('/', authenticate, taskController.createTask);

// Get task by ID
router.get('/:id', authenticate, taskController.getTaskById);

// Update task
router.put('/:id', authenticate, taskController.updateTask);

// Delete task
router.delete('/:id', authenticate, taskController.deleteTask);

// Move task (for Kanban)
router.put('/:id/move', authenticate, taskController.moveTask);

// Add comment to task
router.post('/:id/comments', authenticate, taskController.addTaskComment);

// Add checklist item
router.post('/:id/checklist', authenticate, taskController.addChecklistItem);

// Update checklist item
router.put('/:id/checklist/:itemId', authenticate, taskController.updateChecklistItem);

export default router;
