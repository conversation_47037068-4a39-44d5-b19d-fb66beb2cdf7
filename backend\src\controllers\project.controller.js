import models from '../models/index.js';

const { Project, ProjectTeamMember, User, Company } = models;

// Get all projects
const getProjects = async (req, res) => {
  try {
    const { companyId, status, createdBy } = req.query;

    // Build filter object
    const filters = {};

    // If user is not admin, only show projects from their company
    if (req.user.role !== 'admin') {
      filters.companyId = req.user.companyId;
    } else if (companyId) {
      filters.companyId = companyId;
    }

    if (status) filters.status = status;
    if (createdBy) filters.createdBy = createdBy;

    const projects = await Project.findAll({
      where: filters,
      include: [
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email']
        },
        {
          model: Company,
          attributes: ['id', 'name']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.status(200).json({
      success: true,
      data: projects
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch projects'
    });
  }
};

// Get project by ID
const getProjectById = async (req, res) => {
  try {
    const { id } = req.params;

    const project = await Project.getProjectWithDetails(id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if user has access to this project
    if (req.user.role !== 'admin' && req.user.companyId !== project.companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this project'
      });
    }

    res.status(200).json({
      success: true,
      data: project
    });
  } catch (error) {
    console.error('Get project by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch project'
    });
  }
};

// Create new project
const createProject = async (req, res) => {
  try {
    const { title, description, companyId, dueDate, projectFormat = 'statuses' } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!title || !companyId) {
      return res.status(400).json({
        success: false,
        message: 'Title and company ID are required'
      });
    }

    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to create project in this company'
      });
    }

    // Create project
    const project = await Project.create({
      title,
      description,
      companyId,
      createdBy: userId,
      dueDate,
      projectFormat
    });

    // Add creator as team member
    await Project.addTeamMember(project.id, userId, 'project manager');

    // Get project with details
    const projectWithDetails = await Project.getProjectWithDetails(project.id);

    res.status(201).json({
      success: true,
      data: projectWithDetails,
      message: 'Project created successfully'
    });
  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create project'
    });
  }
};

// Update project
const updateProject = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, dueDate, status } = req.body;

    const project = await Project.findByPk(id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check permissions
    const canUpdate = req.user.role === 'admin' ||
                     project.createdBy === req.user.id ||
                     (req.user.role === 'manager' && req.user.companyId === project.companyId);

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update this project'
      });
    }

    // Update project
    await project.update({
      title: title || project.title,
      description: description !== undefined ? description : project.description,
      dueDate: dueDate !== undefined ? dueDate : project.dueDate,
      status: status || project.status
    });

    // Get updated project with details
    const updatedProject = await Project.getProjectWithDetails(id);

    res.status(200).json({
      success: true,
      data: updatedProject,
      message: 'Project updated successfully'
    });
  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update project'
    });
  }
};

// Delete project
const deleteProject = async (req, res) => {
  try {
    const { id } = req.params;

    const project = await Project.findByPk(id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check permissions
    const canDelete = req.user.role === 'admin' ||
                     project.createdBy === req.user.id ||
                     (req.user.role === 'manager' && req.user.companyId === project.companyId);

    if (!canDelete) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to delete this project'
      });
    }

    await project.destroy();

    res.status(200).json({
      success: true,
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Delete project error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete project'
    });
  }
};

// Add member to project
const addProjectMember = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, role } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    const project = await Project.findByPk(id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check permissions
    const canAddMember = req.user.role === 'admin' ||
                        project.createdBy === req.user.id ||
                        (req.user.role === 'manager' && req.user.companyId === project.companyId);

    if (!canAddMember) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to add members to this project'
      });
    }

    // Check if user exists and is in the same company
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.companyId !== project.companyId) {
      return res.status(400).json({
        success: false,
        message: 'User must be in the same company as the project'
      });
    }

    // Check if user is already a member
    const existingMember = await ProjectTeamMember.findOne({
      where: { projectId: id, userId }
    });

    if (existingMember) {
      return res.status(400).json({
        success: false,
        message: 'User is already a member of this project'
      });
    }

    // Add member
    await Project.addTeamMember(id, userId, role);

    res.status(200).json({
      success: true,
      message: 'Member added to project successfully'
    });
  } catch (error) {
    console.error('Add project member error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add member to project'
    });
  }
};

// Remove member from project
const removeProjectMember = async (req, res) => {
  try {
    const { id, userId } = req.params;

    const project = await Project.findByPk(id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check permissions
    const canRemoveMember = req.user.role === 'admin' ||
                           project.createdBy === req.user.id ||
                           (req.user.role === 'manager' && req.user.companyId === project.companyId);

    if (!canRemoveMember) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to remove members from this project'
      });
    }

    // Cannot remove project creator
    if (project.createdBy === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot remove project creator from project'
      });
    }

    const removed = await Project.removeTeamMember(id, userId);

    if (!removed) {
      return res.status(404).json({
        success: false,
        message: 'User is not a member of this project'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Member removed from project successfully'
    });
  } catch (error) {
    console.error('Remove project member error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove member from project'
    });
  }
};

export {
  getProjects,
  getProjectById,
  createProject,
  updateProject,
  deleteProject,
  addProjectMember,
  removeProjectMember
};
