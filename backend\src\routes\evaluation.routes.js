import express from 'express';
import * as evaluationController from '../controllers/evaluation.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Get evaluations
router.get('/', authenticate, evaluationController.getEvaluations);

// Create evaluation
router.post('/', authenticate, authorize(['admin', 'manager']), evaluationController.createEvaluation);

// Get evaluation by ID
router.get('/:id', authenticate, evaluationController.getEvaluationById);

// Update evaluation
router.put('/:id', authenticate, evaluationController.updateEvaluation);

// Delete evaluation
router.delete('/:id', authenticate, authorize(['admin', 'manager']), evaluationController.deleteEvaluation);

// Complete evaluation
router.put('/:id/complete', authenticate, evaluationController.completeEvaluation);

export default router;
