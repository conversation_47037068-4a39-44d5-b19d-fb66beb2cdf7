import models from '../models/index.js';

const { User, Company } = models;

// Get all users
const getUsers = async (req, res) => {
  try {
    const { companyId, role, status } = req.query;

    // Build filter object
    const filters = {};

    // If user is not admin, only show users from their company
    if (req.user.role !== 'admin') {
      filters.companyId = req.user.companyId;
    } else if (companyId) {
      filters.companyId = companyId;
    }

    if (role) filters.role = role;
    if (status) filters.status = status;

    const users = await User.findAll({
      where: filters,
      attributes: { exclude: ['password'] },
      include: [{
        model: Company,
        attributes: ['id', 'name']
      }]
    });

    res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
};

// Get user by ID
const getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] },
      include: [{
        model: Company,
        attributes: ['id', 'name']
      }]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if user has access to view this user
    if (req.user.role !== 'admin' && req.user.companyId !== user.companyId && req.user.id !== id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to view this user'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user'
    });
  }
};

// Update user
const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, position, department, avatar, status, role, companyId } = req.body;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check permissions
    const canUpdate = req.user.id === id || // User updating themselves
                     req.user.role === 'admin' || // Admin can update anyone
                     (req.user.role === 'manager' && req.user.companyId === user.companyId); // Manager can update users in same company

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update this user'
      });
    }

    // Prepare update data
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (position !== undefined) updateData.position = position;
    if (department !== undefined) updateData.department = department;
    if (avatar !== undefined) updateData.avatar = avatar;

    // Only admin or manager can update status and role
    if (req.user.role === 'admin' || req.user.role === 'manager') {
      if (status !== undefined) updateData.status = status;
      if (role !== undefined) updateData.role = role;
      if (companyId !== undefined) updateData.companyId = companyId;
    }

    // Update user
    await user.update(updateData);

    // Get updated user without password
    const updatedUser = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    res.status(200).json({
      success: true,
      data: updatedUser,
      message: 'User updated successfully'
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user'
    });
  }
};

// Delete user
const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check permissions - only admin or company owner can delete users
    const canDelete = req.user.role === 'admin' ||
                     (req.user.companyId === user.companyId && req.user.role === 'manager');

    if (!canDelete) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to delete this user'
      });
    }

    // Prevent self-deletion
    if (req.user.id === id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account'
      });
    }

    await user.destroy();

    res.status(200).json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete user'
    });
  }
};

// Invite new user
const inviteUser = async (req, res) => {
  try {
    const { email, name, role = 'employee', companyId } = req.body;

    if (!email || !name) {
      return res.status(400).json({
        success: false,
        message: 'Email and name are required'
      });
    }

    // Check if user has permission to invite
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to invite users'
      });
    }

    // Use requester's company if not specified and not admin
    const targetCompanyId = req.user.role === 'admin' ? companyId : req.user.companyId;

    if (!targetCompanyId) {
      return res.status(400).json({
        success: false,
        message: 'Company ID is required'
      });
    }

    // Check if user already exists
    const existingUser = await User.findByEmail(email);

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // TODO: Send invitation email with temporary password or registration link
    // For now, we'll just return success message

    res.status(200).json({
      success: true,
      message: 'Invitation sent successfully (email functionality to be implemented)',
      data: {
        email,
        name,
        role,
        companyId: targetCompanyId
      }
    });
  } catch (error) {
    console.error('Invite user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to invite user'
    });
  }
};

// Get users by company
const getUsersByCompany = async (req, res) => {
  try {
    const { companyId } = req.params;

    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this company'
      });
    }

    const users = await User.findByCompany(companyId);

    res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get users by company error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
};

export {
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  inviteUser,
  getUsersByCompany
};
