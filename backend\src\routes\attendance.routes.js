import express from 'express';
import * as attendanceController from '../controllers/attendance.controller.js';
import { authenticate, authorize } from '../middleware/auth.middleware.js';

const router = express.Router();

// Get attendance records
router.get('/', authenticate, attendanceController.getAttendance);

// Record check-in
router.post('/check-in', authenticate, attendanceController.checkIn);

// Record check-out
router.post('/check-out', authenticate, attendanceController.checkOut);

// Get daily overview
router.get('/daily', authenticate, authorize(['admin', 'manager']), attendanceController.getDailyOverview);

// Get detailed logs
router.get('/logs', authenticate, attendanceController.getDetailedLogs);

// Get hours breakdown
router.get('/hourmap', authenticate, attendanceController.getHourMap);

export default router;
