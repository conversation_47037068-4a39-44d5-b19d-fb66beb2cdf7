import Waitlist from "../models/waitlist.model.js";
import { sendWaitlistConfirmation } from "../utils/emailService.js";
import { Op } from "sequelize";
import { sequelize } from "../config/database.js";

// Add email to waitlist
const joinWaitlist = async (req, res) => {
  try {
    const { email, source } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get("User-Agent");

    // Validate email
    if (!email || !email.includes("@")) {
      return res.status(400).json({
        success: false,
        message: "Valid email address is required",
      });
    }

    // Check if email already exists
    const existingEntry = await Waitlist.findOne({ where: { email } });
    if (existingEntry) {
      return res.status(200).json({
        success: true,
        message: "You are already on our waitlist!",
        data: {
          email,
          subscriptionDate: existingEntry.subscriptionDate,
        },
      });
    }

    // Create waitlist entry
    const waitlistEntry = await Waitlist.create({
      email,
      ipAddress,
      userAgent,
      source: source || "landing_page",
    });

    // Send confirmation email
    const emailResult = await sendWaitlistConfirmation(email);

    // Update email status
    if (emailResult.success) {
      await waitlistEntry.update({
        emailSent: true,
        emailSentAt: new Date(),
      });
    }

    res.status(201).json({
      success: true,
      message: "Successfully joined the waitlist!",
      data: {
        id: waitlistEntry.id,
        email: waitlistEntry.email,
        subscriptionDate: waitlistEntry.subscriptionDate,
        emailSent: emailResult.success,
      },
    });
  } catch (error) {
    console.error("Join waitlist error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to join waitlist",
    });
  }
};

// Get waitlist statistics (for admin)
const getWaitlistStats = async (req, res) => {
  try {
    const totalCount = await Waitlist.count();
    const emailsSent = await Waitlist.count({ where: { emailSent: true } });

    const recentSignups = await Waitlist.findAll({
      order: [["createdAt", "DESC"]],
      limit: 10,
      attributes: ["email", "createdAt", "source", "emailSent"],
    });

    // Get signups by day for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailySignups = await Waitlist.findAll({
      where: {
        createdAt: {
          [Op.gte]: thirtyDaysAgo,
        },
      },
      attributes: [
        [sequelize.fn("DATE", sequelize.col("createdAt")), "date"],
        [sequelize.fn("COUNT", sequelize.col("id")), "count"],
      ],
      group: [sequelize.fn("DATE", sequelize.col("createdAt"))],
      order: [[sequelize.fn("DATE", sequelize.col("createdAt")), "ASC"]],
    });

    res.status(200).json({
      success: true,
      data: {
        totalCount,
        emailsSent,
        recentSignups,
        dailySignups,
      },
    });
  } catch (error) {
    console.error("Get waitlist stats error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get waitlist statistics",
    });
  }
};

// Get all waitlist entries (for admin)
const getAllWaitlistEntries = async (req, res) => {
  try {
    const { page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows } = await Waitlist.findAndCountAll({
      order: [["createdAt", "DESC"]],
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: [
        "id",
        "email",
        "createdAt",
        "source",
        "emailSent",
        "emailSentAt",
      ],
    });

    res.status(200).json({
      success: true,
      data: {
        entries: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    console.error("Get all waitlist entries error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get waitlist entries",
    });
  }
};

export { joinWaitlist, getWaitlistStats, getAllWaitlistEntries };
