import models from '../models/index.js';

const { Company, User } = models;

// Get all companies for current user
const getCompanies = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get companies where user is owner or member
    const companies = await Company.findForUser(userId);

    res.status(200).json({
      success: true,
      data: companies
    });
  } catch (error) {
    console.error('Get companies error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch companies'
    });
  }
};

// Get company by ID
const getCompanyById = async (req, res) => {
  try {
    const { id } = req.params;

    const company = await Company.findByPk(id);

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this company'
      });
    }

    res.status(200).json({
      success: true,
      data: company
    });
  } catch (error) {
    console.error('Get company by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch company'
    });
  }
};

// Create new company
const createCompany = async (req, res) => {
  try {
    const {
      companyName: name,
      description,
      logo,
      workingHoursStart: checkInHoursStart,
      workingHoursEnd: checkInHoursEnd,
      messageFormat,
      timezone,
      country
    } = req.body;

    const userId = req.user.id;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Company name is required'
      });
    }

    // Create company
    const company = await Company.create({
      name,
      description,
      logo,
      ownerId: userId,
      checkInHoursStart,
      checkInHoursEnd,
      messageFormat,
      timezone,
      country
    });

    // Update user's companyId to the new company
    await User.update(
      { companyId: company.id },
      { where: { id: userId } }
    );

    res.status(201).json({
      success: true,
      data: company,
      message: 'Company created successfully'
    });
  } catch (error) {
    console.error('Create company error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create company'
    });
  }
};

// Update company
const updateCompany = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, logo, checkInHoursStart, checkInHoursEnd, messageFormat } = req.body;

    const company = await Company.findByPk(id);

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Check if user is owner or admin
    if (req.user.role !== 'admin' && company.ownerId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Only company owner or admin can update company'
      });
    }

    // Update company
    await company.update({
      name: name || company.name,
      description: description !== undefined ? description : company.description,
      logo: logo !== undefined ? logo : company.logo,
      checkInHoursStart: checkInHoursStart !== undefined ? checkInHoursStart : company.checkInHoursStart,
      checkInHoursEnd: checkInHoursEnd !== undefined ? checkInHoursEnd : company.checkInHoursEnd,
      messageFormat: messageFormat !== undefined ? messageFormat : company.messageFormat
    });

    res.status(200).json({
      success: true,
      data: company,
      message: 'Company updated successfully'
    });
  } catch (error) {
    console.error('Update company error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update company'
    });
  }
};

// Delete company
const deleteCompany = async (req, res) => {
  try {
    const { id } = req.params;

    const company = await Company.findByPk(id);

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Check if user is owner or admin
    if (req.user.role !== 'admin' && company.ownerId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Only company owner or admin can delete company'
      });
    }

    // Update all users in this company to have null companyId
    await User.update(
      { companyId: null },
      { where: { companyId: id } }
    );

    // Delete company
    await company.destroy();

    res.status(200).json({
      success: true,
      message: 'Company deleted successfully'
    });
  } catch (error) {
    console.error('Delete company error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete company'
    });
  }
};

// Get users in company
const getCompanyUsers = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this company'
      });
    }

    const users = await Company.getUsers(id);

    res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get company users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch company users'
    });
  }
};

// Invite user to company
const inviteUserToCompany = async (req, res) => {
  try {
    const { id } = req.params;
    const { email, role = 'employee' } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    const company = await Company.findByPk(id);

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Check if user has permission to invite
    if (req.user.role !== 'admin' && req.user.role !== 'manager' && company.ownerId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to invite users'
      });
    }

    // Check if user already exists
    const existingUser = await User.findByEmail(email);

    if (existingUser) {
      // Update existing user's company
      await existingUser.update({ companyId: id, role });

      res.status(200).json({
        success: true,
        message: 'User added to company successfully',
        data: {
          id: existingUser.id,
          email: existingUser.email,
          name: existingUser.name,
          role: existingUser.role
        }
      });
    } else {
      // TODO: Send invitation email to new user
      res.status(200).json({
        success: true,
        message: 'Invitation sent successfully (email functionality to be implemented)'
      });
    }
  } catch (error) {
    console.error('Invite user to company error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to invite user to company'
    });
  }
};

export {
  getCompanies,
  getCompanyById,
  createCompany,
  updateCompany,
  deleteCompany,
  getCompanyUsers,
  inviteUserToCompany
};
