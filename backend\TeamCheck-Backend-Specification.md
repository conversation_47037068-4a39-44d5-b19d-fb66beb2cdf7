# TeamCheck Backend Specification

## Overview

This document outlines the backend requirements for the TeamCheck application, including the necessary API endpoints, data models, authentication mechanisms, and integration points. The backend will be built using Node.js with Express and MySQL for data storage.

## Technology Stack

- **Runtime Environment**: Node.js
- **Framework**: Express.js
- **Database**: MySQL
- **Authentication**: JWT (JSON Web Tokens)
- **API Format**: RESTful

## Data Models

### User Model

```sql
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  position VARCHAR(255),
  role ENUM('admin', 'manager', 'employee') NOT NULL,
  avatar VARCHAR(255),
  dateJoined DATETIME NOT NULL,
  department VARCHAR(255),
  status ENUM('active', 'inactive', 'on leave') DEFAULT 'active',
  workspaceId VARCHAR(36) NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOR<PERSON>G<PERSON> KEY (workspaceId) REFERENCES workspaces(id)
);
```

### Workspace Model

```sql
CREATE TABLE workspaces (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  logo VARCHAR(255),
  ownerId VARCHAR(36) NOT NULL,
  checkInHoursStart VARCHAR(5),
  checkInHoursEnd VARCHAR(5),
  messageFormat VARCHAR(255),
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);
```

### Project Model

```sql
CREATE TABLE projects (
  id VARCHAR(36) PRIMARY KEY,
  workspaceId VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  createdBy VARCHAR(36) NOT NULL,
  createdAt DATETIME NOT NULL,
  dueDate DATETIME,
  status ENUM('not started', 'in progress', 'completed') DEFAULT 'not started',
  projectFormat ENUM('statuses', 'milestones') DEFAULT 'statuses',
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (workspaceId) REFERENCES workspaces(id),
  FOREIGN KEY (createdBy) REFERENCES users(id)
);
```

### Project Team Members

```sql
CREATE TABLE project_team_members (
  id VARCHAR(36) PRIMARY KEY,
  projectId VARCHAR(36) NOT NULL,
  userId VARCHAR(36) NOT NULL,
  role VARCHAR(255),
  FOREIGN KEY (projectId) REFERENCES projects(id),
  FOREIGN KEY (userId) REFERENCES users(id)
);
```

### Project Columns

```sql
CREATE TABLE project_columns (
  id VARCHAR(36) PRIMARY KEY,
  projectId VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  color VARCHAR(7),
  FOREIGN KEY (projectId) REFERENCES projects(id)
);
```

### Project Milestones

```sql
CREATE TABLE project_milestones (
  id VARCHAR(36) PRIMARY KEY,
  projectId VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  dueDate DATETIME,
  status ENUM('not started', 'in progress', 'completed') DEFAULT 'not started',
  completedAt DATETIME,
  FOREIGN KEY (projectId) REFERENCES projects(id)
);
```

### Task Model

```sql
CREATE TABLE tasks (
  id VARCHAR(36) PRIMARY KEY,
  workspaceId VARCHAR(36) NOT NULL,
  projectId VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  status ENUM('not started', 'in progress', 'completed') DEFAULT 'not started',
  columnId VARCHAR(36),
  dueDate DATETIME,
  createdBy VARCHAR(36) NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (workspaceId) REFERENCES workspaces(id),
  FOREIGN KEY (projectId) REFERENCES projects(id),
  FOREIGN KEY (columnId) REFERENCES project_columns(id),
  FOREIGN KEY (createdBy) REFERENCES users(id)
);
```

### Task Assignees

```sql
CREATE TABLE task_assignees (
  id VARCHAR(36) PRIMARY KEY,
  taskId VARCHAR(36) NOT NULL,
  userId VARCHAR(36) NOT NULL,
  FOREIGN KEY (taskId) REFERENCES tasks(id),
  FOREIGN KEY (userId) REFERENCES users(id)
);
```

### Task Labels

```sql
CREATE TABLE task_labels (
  id VARCHAR(36) PRIMARY KEY,
  taskId VARCHAR(36) NOT NULL,
  name VARCHAR(255) NOT NULL,
  color VARCHAR(7),
  FOREIGN KEY (taskId) REFERENCES tasks(id)
);
```

### Task Checklist

```sql
CREATE TABLE task_checklist_items (
  id VARCHAR(36) PRIMARY KEY,
  taskId VARCHAR(36) NOT NULL,
  text TEXT NOT NULL,
  completed BOOLEAN DEFAULT false,
  FOREIGN KEY (taskId) REFERENCES tasks(id)
);
```

### Task Comments

```sql
CREATE TABLE task_comments (
  id VARCHAR(36) PRIMARY KEY,
  taskId VARCHAR(36) NOT NULL,
  userId VARCHAR(36) NOT NULL,
  text TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  FOREIGN KEY (taskId) REFERENCES tasks(id),
  FOREIGN KEY (userId) REFERENCES users(id)
);
```

### Attendance Model

```sql
CREATE TABLE attendance (
  id VARCHAR(36) PRIMARY KEY,
  workspaceId VARCHAR(36) NOT NULL,
  userId VARCHAR(36) NOT NULL,
  date DATE NOT NULL,
  checkInTime DATETIME,
  checkInMessage TEXT,
  checkOutTime DATETIME,
  checkOutMessage TEXT,
  status ENUM('present', 'absent', 'late', 'on leave') DEFAULT 'absent',
  hoursWorked DECIMAL(5,2),
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (workspaceId) REFERENCES workspaces(id),
  FOREIGN KEY (userId) REFERENCES users(id)
);
```

### Project Hours

```sql
CREATE TABLE project_hours (
  id VARCHAR(36) PRIMARY KEY,
  attendanceId VARCHAR(36) NOT NULL,
  projectId VARCHAR(36) NOT NULL,
  hours DECIMAL(5,2) NOT NULL,
  FOREIGN KEY (attendanceId) REFERENCES attendance(id),
  FOREIGN KEY (projectId) REFERENCES projects(id)
);
```

### Leave Model

```sql
CREATE TABLE leaves (
  id VARCHAR(36) PRIMARY KEY,
  workspaceId VARCHAR(36) NOT NULL,
  userId VARCHAR(36) NOT NULL,
  startDate DATE NOT NULL,
  endDate DATE NOT NULL,
  reason TEXT,
  status ENUM('pending', 'approved', 'denied') DEFAULT 'pending',
  approvedBy VARCHAR(36),
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (workspaceId) REFERENCES workspaces(id),
  FOREIGN KEY (userId) REFERENCES users(id),
  FOREIGN KEY (approvedBy) REFERENCES users(id)
);
```

### Evaluation Model

```sql
CREATE TABLE evaluations (
  id VARCHAR(36) PRIMARY KEY,
  workspaceId VARCHAR(36) NOT NULL,
  type ENUM('periodic', 'project') NOT NULL,
  employeeId VARCHAR(36) NOT NULL,
  evaluatorId VARCHAR(36) NOT NULL,
  projectId VARCHAR(36),
  date DATE NOT NULL,
  status ENUM('in progress', 'completed') DEFAULT 'in progress',
  performanceRating TINYINT,
  teamworkRating TINYINT,
  communicationRating TINYINT,
  initiativeRating TINYINT,
  reliabilityRating TINYINT,
  feedback TEXT,
  createdAt DATETIME NOT NULL,
  completedAt DATETIME,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (workspaceId) REFERENCES workspaces(id),
  FOREIGN KEY (employeeId) REFERENCES users(id),
  FOREIGN KEY (evaluatorId) REFERENCES users(id),
  FOREIGN KEY (projectId) REFERENCES projects(id)
);
```

### Evaluation Goals

```sql
CREATE TABLE evaluation_goals (
  id VARCHAR(36) PRIMARY KEY,
  evaluationId VARCHAR(36) NOT NULL,
  goal TEXT NOT NULL,
  FOREIGN KEY (evaluationId) REFERENCES evaluations(id)
);
```

## API Endpoints

### Workspaces

| Endpoint | Method | Description | Request Body/Params | Response |
|----------|--------|-------------|--------------|----------|
| `/api/workspaces` | GET | Get all workspaces for current user | Query params for filtering | `{ workspaces }` |
| `/api/workspaces` | POST | Create new workspace | `{ name, description, logo, settings }` | `{ workspace }` |
| `/api/workspaces/:id` | GET | Get workspace by ID | - | `{ workspace }` |
| `/api/workspaces/:id` | PUT | Update workspace | `{ name, description, settings, etc. }` | `{ workspace }` |
| `/api/workspaces/:id` | DELETE | Delete workspace | - | `{ success }` |
| `/api/workspaces/:id/users` | GET | Get all users in workspace | Query params for filtering | `{ users }` |
| `/api/workspaces/:id/invite` | POST | Invite user to workspace | `{ email, role, position }` | `{ success }` |

### Authentication

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/auth/register` | POST | Register a new user | `{ email, password, name, position, workspaceId }` | `{ user, token }` |
| `/api/auth/login` | POST | Login a user | `{ email, password }` | `{ user, token }` |
| `/api/auth/verify-email` | POST | Verify user email | `{ token }` | `{ success }` |
| `/api/auth/forgot-password` | POST | Request password reset | `{ email }` | `{ success }` |
| `/api/auth/reset-password` | POST | Reset password | `{ token, password }` | `{ success }` |
| `/api/auth/me` | GET | Get current user | - | `{ user }` |

### Users

| Endpoint | Method | Description | Request Body/Params | Response |
|----------|--------|-------------|--------------|----------|
| `/api/users` | GET | Get all users | Query params for filtering including `workspaceId` | `{ users }` |
| `/api/users/:id` | GET | Get user by ID | - | `{ user }` |
| `/api/users/:id` | PUT | Update user | `{ name, position, role, workspaceId, etc. }` | `{ user }` |
| `/api/users/:id` | DELETE | Delete user | - | `{ success }` |
| `/api/users/invite` | POST | Invite new user | `{ email, role, position, workspaceId }` | `{ success }` |
| `/api/users/workspace/:workspaceId` | GET | Get all users in a workspace | Query params for filtering | `{ users }` |

### Projects

| Endpoint | Method | Description | Request Body/Params | Response |
|----------|--------|-------------|--------------|----------|
| `/api/projects` | GET | Get all projects | Query params for filtering | `{ projects }` |
| `/api/projects` | POST | Create new project | `{ title, description, dueDate, teamMembers, projectFormat }` | `{ project }` |
| `/api/projects/:id` | GET | Get project by ID | - | `{ project }` |
| `/api/projects/:id` | PUT | Update project | `{ title, description, etc. }` | `{ project }` |
| `/api/projects/:id` | DELETE | Delete project | - | `{ success }` |
| `/api/projects/:id/members` | POST | Add members to project | `{ members: [{ userId, role }] }` | `{ project }` |
| `/api/projects/:id/members/:userId` | DELETE | Remove member from project | - | `{ success }` |

### Tasks

| Endpoint | Method | Description | Request Body/Params | Response |
|----------|--------|-------------|--------------|----------|
| `/api/tasks` | GET | Get all tasks | Query params for filtering | `{ tasks }` |
| `/api/tasks` | POST | Create new task | `{ projectId, title, description, assignees, etc. }` | `{ task }` |
| `/api/tasks/:id` | GET | Get task by ID | - | `{ task }` |
| `/api/tasks/:id` | PUT | Update task | `{ title, status, assignees, etc. }` | `{ task }` |
| `/api/tasks/:id` | DELETE | Delete task | - | `{ success }` |
| `/api/tasks/:id/move` | PUT | Move task in Kanban | `{ sourceColumnId, destinationColumnId, sourceIndex, destinationIndex }` | `{ task }` |
| `/api/tasks/:id/comments` | POST | Add comment to task | `{ text }` | `{ comment }` |
| `/api/tasks/:id/checklist` | POST | Add checklist item | `{ text }` | `{ item }` |
| `/api/tasks/:id/checklist/:itemId` | PUT | Update checklist item | `{ completed }` | `{ item }` |

### Attendance

| Endpoint | Method | Description | Request Body/Params | Response |
|----------|--------|-------------|--------------|----------|
| `/api/attendance` | GET | Get attendance records | Query params for filtering | `{ records }` |
| `/api/attendance/check-in` | POST | Record check-in | `{ message }` | `{ record }` |
| `/api/attendance/check-out` | POST | Record check-out | `{ message }` | `{ record }` |
| `/api/attendance/daily` | GET | Get daily overview | `{ date }` | `{ overview }` |
| `/api/attendance/logs` | GET | Get detailed logs | Query params for filtering | `{ logs }` |
| `/api/attendance/hourmap` | GET | Get hours breakdown | Query params for filtering | `{ hourmap }` |

### Leaves

| Endpoint | Method | Description | Request Body/Params | Response |
|----------|--------|-------------|--------------|----------|
| `/api/leaves` | GET | Get leave requests | Query params for filtering | `{ leaves }` |
| `/api/leaves` | POST | Create leave request | `{ startDate, endDate, reason }` | `{ leave }` |
| `/api/leaves/:id` | GET | Get leave by ID | - | `{ leave }` |
| `/api/leaves/:id` | PUT | Update leave request | `{ status, etc. }` | `{ leave }` |
| `/api/leaves/:id/approve` | PUT | Approve leave | - | `{ leave }` |
| `/api/leaves/:id/deny` | PUT | Deny leave | `{ reason }` | `{ leave }` |

### Evaluations

| Endpoint | Method | Description | Request Body/Params | Response |
|----------|--------|-------------|--------------|----------|
| `/api/evaluations` | GET | Get evaluations | Query params for filtering | `{ evaluations }` |
| `/api/evaluations` | POST | Create evaluation | `{ type, employeeId, projectId, date }` | `{ evaluation }` |
| `/api/evaluations/:id` | GET | Get evaluation by ID | - | `{ evaluation }` |
| `/api/evaluations/:id` | PUT | Update evaluation | `{ ratings, feedback, etc. }` | `{ evaluation }` |
| `/api/evaluations/:id` | DELETE | Delete evaluation | - | `{ success }` |
| `/api/evaluations/:id/complete` | PUT | Complete evaluation | `{ ratings, feedback, goals }` | `{ evaluation }` |


## Authentication & Authorization

### JWT Authentication

- JWT tokens will be used for authentication
- Tokens will be included in the Authorization header as Bearer tokens
- Tokens will expire after 24 hours
- Refresh tokens will be implemented for seamless re-authentication

### Role-Based Access Control

Three main roles with different permissions:

1. **Admin**:
   - Full access to all features
   - Can manage users, roles, and company settings
   - Can view and modify all projects, tasks, and evaluations

2. **Manager**:
   - Can create and manage projects
   - Can assign tasks to team members
   - Can view and create evaluations
   - Can approve/deny leave requests
   - Limited access to user management

3. **Employee**:
   - Can view assigned tasks and projects
   - Can update task status and add comments
   - Can request leaves
   - Can view own evaluations
   - Cannot access management features

## Implementation Considerations

1. **Error Handling**:
   - Consistent error response format
   - Appropriate HTTP status codes
   - Detailed error messages for debugging

2. **Validation**:
   - Input validation for all API endpoints
   - Data sanitization to prevent injection attacks

3. **Logging**:
   - Request logging for debugging and monitoring
   - Error logging for troubleshooting
   - Audit logging for security events

4. **Performance**:
   - Database indexing for frequently queried fields
   - Pagination for large data sets
   - Caching for frequently accessed data

5. **Security**:
   - HTTPS for all API endpoints
   - Rate limiting to prevent abuse
   - CORS configuration for frontend access
   - Input sanitization to prevent XSS attacks
   - SQL injection prevention

## Deployment

The backend will be deployed on:
- AWS EC2 or similar cloud service
- Using Docker containers for consistency
- With a CI/CD pipeline for automated testing and deployment

## Monitoring & Maintenance

- Health check endpoints for monitoring
- Performance metrics collection
- Automated backups of database
- Scheduled maintenance windows





